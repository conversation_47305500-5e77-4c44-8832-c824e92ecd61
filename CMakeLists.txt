cmake_minimum_required(VERSION 3.16)

project(VideoEditor VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets)

# Enable automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    src/main.cpp
    src/mainwindow.cpp
    src/videoplayer.cpp
    src/timeline.cpp
    src/videoeditor.cpp
)

# Header files
set(HEADERS
    src/mainwindow.h
    src/videoplayer.h
    src/timeline.h
    src/videoeditor.h
)

# Create the executable
add_executable(VideoEditor ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(VideoEditor
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::MultimediaWidgets
)

# Set target properties
set_target_properties(VideoEditor PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Include directories
target_include_directories(VideoEditor PRIVATE src)

# Windows deployment - copy Qt DLLs automatically
if(WIN32)
    # Find Qt installation path
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_WINDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_WINDEPLOYQT_EXECUTABLE "${QT_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")

    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_custom_command(TARGET VideoEditor POST_BUILD
            COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --qmldir ${CMAKE_SOURCE_DIR} $<TARGET_FILE_DIR:VideoEditor>
            COMMENT "Deploying Qt libraries")
    else()
        add_custom_command(TARGET VideoEditor POST_BUILD
            COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --debug --qmldir ${CMAKE_SOURCE_DIR} $<TARGET_FILE_DIR:VideoEditor>
            COMMENT "Deploying Qt libraries")
    endif()
endif()
