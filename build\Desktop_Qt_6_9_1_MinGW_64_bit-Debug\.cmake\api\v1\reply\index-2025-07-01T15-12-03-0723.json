{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "C:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "C:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "C:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-24652661b587824bcedf.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-dd327ef220989aec88b7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5624e891327760f50ba5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-dd327ef220989aec88b7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5624e891327760f50ba5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-24652661b587824bcedf.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}