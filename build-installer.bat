@echo off
REM Build installer for Puttis VideoEditor
REM This script creates a professional Windows installer

echo ========================================
echo Puttis VideoEditor Installer Builder
echo ========================================
echo.

REM Check if NSIS is installed
set NSIS_PATH="C:\Program Files (x86)\NSIS\makensis.exe"
if not exist %NSIS_PATH% (
    echo ERROR: NSIS not found at %NSIS_PATH%
    echo.
    echo Please install NSIS from: https://nsis.sourceforge.io/Download
    echo After installation, run this script again.
    echo.
    pause
    exit /b 1
)

REM Check if executable exists
if not exist "build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\VideoEditor.exe" (
    echo ERROR: VideoEditor.exe not found!
    echo Please build the project first in Qt Creator.
    echo.
    pause
    exit /b 1
)

echo Building installer...
echo.

REM Build the installer
%NSIS_PATH% installer.nsi

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS! Installer created successfully!
    echo ========================================
    echo.
    echo Installer file: PuttisVideoEditor_Setup.exe
    echo.
    echo You can now distribute this installer to install
    echo Puttis VideoEditor on any Windows computer!
    echo.
    echo The installer will:
    echo - Install Puttis VideoEditor to user's AppData folder
    echo - Copy all required Qt DLLs automatically
    echo - Create Start Menu shortcuts
    echo - Create Desktop shortcut
    echo - Add to Add/Remove Programs
    echo - Include uninstaller
    echo.
) else (
    echo.
    echo ERROR: Failed to build installer!
    echo Check the error messages above.
    echo.
)

pause
