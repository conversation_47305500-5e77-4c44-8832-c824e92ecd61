#ifndef VIDEOPLAYER_H
#define VIDEOPLAYER_H

#include <QWidget>
#include <QMediaPlayer>
#include <QVideoWidget>
#include <QAudioOutput>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QTimer>
#include <QUrl>
#include <QFileInfo>

class VideoPlayer : public QWidget
{
    Q_OBJECT

public:
    explicit VideoPlayer(QWidget *parent = nullptr);
    ~VideoPlayer();

    // Playback control methods
    void loadVideo(const QString &filePath);
    void play();
    void pause();
    void stop();
    void setPosition(qint64 position);
    void setVolume(int volume);
    
    // State queries
    bool isPlaying() const;
    qint64 position() const;
    qint64 duration() const;
    
    // Slider interaction
    void setSliderPressed(bool pressed);

signals:
    void videoLoaded(const QString &fileName);
    void errorOccurred(const QString &error);
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);
    void playbackStateChanged(QMediaPlayer::PlaybackState state);

private slots:
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    void onPlaybackStateChanged(QMediaPlayer::PlaybackState state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onErrorOccurred(QMediaPlayer::Error error, const QString &errorString);

private:
    void setupUI();
    void setupMediaPlayer();
    void connectSignals();
    QString formatTime(qint64 milliseconds) const;

    // Media components
    QMediaPlayer *m_mediaPlayer;
    QVideoWidget *m_videoWidget;
    
    // UI components
    QVBoxLayout *m_mainLayout;
    QLabel *m_videoLabel;
    QLabel *m_statusLabel;
    
    // State variables
    QString m_currentFile;
    bool m_sliderPressed;
    bool m_videoLoaded;
    
    // Timer for position updates
    QTimer *m_positionTimer;
};

#endif // VIDEOPLAYER_H
