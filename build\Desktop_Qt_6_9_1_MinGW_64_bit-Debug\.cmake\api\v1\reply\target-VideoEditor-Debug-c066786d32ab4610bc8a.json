{"artifacts": [{"path": "VideoEditor.exe"}, {"path": "VideoEditor.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_include_directories"], "files": ["CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake", "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Multimedia/Qt6MultimediaDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"command": 4, "file": 0, "line": 9, "parent": 0}, {"command": 4, "file": 3, "line": 297, "parent": 3}, {"file": 2, "parent": 4}, {"command": 3, "file": 2, "line": 55, "parent": 5}, {"file": 1, "parent": 6}, {"command": 2, "file": 1, "line": 61, "parent": 7}, {"command": 3, "file": 2, "line": 43, "parent": 5}, {"file": 8, "parent": 9}, {"command": 6, "file": 8, "line": 45, "parent": 10}, {"command": 5, "file": 7, "line": 137, "parent": 11}, {"command": 4, "file": 6, "line": 76, "parent": 12}, {"command": 4, "file": 3, "line": 315, "parent": 13}, {"file": 5, "parent": 14}, {"command": 3, "file": 5, "line": 55, "parent": 15}, {"file": 4, "parent": 16}, {"command": 2, "file": 4, "line": 61, "parent": 17}, {"command": 4, "file": 3, "line": 297, "parent": 3}, {"file": 10, "parent": 19}, {"command": 3, "file": 10, "line": 55, "parent": 20}, {"file": 9, "parent": 21}, {"command": 2, "file": 9, "line": 61, "parent": 22}, {"command": 4, "file": 3, "line": 297, "parent": 3}, {"file": 12, "parent": 24}, {"command": 3, "file": 12, "line": 57, "parent": 25}, {"file": 11, "parent": 26}, {"command": 2, "file": 11, "line": 61, "parent": 27}, {"command": 3, "file": 12, "line": 45, "parent": 25}, {"file": 15, "parent": 29}, {"command": 6, "file": 15, "line": 46, "parent": 30}, {"command": 5, "file": 7, "line": 137, "parent": 31}, {"command": 4, "file": 6, "line": 76, "parent": 32}, {"command": 4, "file": 3, "line": 315, "parent": 33}, {"file": 14, "parent": 34}, {"command": 3, "file": 14, "line": 55, "parent": 35}, {"file": 13, "parent": 36}, {"command": 2, "file": 13, "line": 61, "parent": 37}, {"command": 2, "file": 13, "line": 83, "parent": 37}, {"command": 3, "file": 10, "line": 43, "parent": 20}, {"file": 18, "parent": 40}, {"command": 6, "file": 18, "line": 45, "parent": 41}, {"command": 5, "file": 7, "line": 137, "parent": 42}, {"command": 4, "file": 6, "line": 76, "parent": 43}, {"command": 4, "file": 3, "line": 315, "parent": 44}, {"file": 17, "parent": 45}, {"command": 3, "file": 17, "line": 55, "parent": 46}, {"file": 16, "parent": 47}, {"command": 2, "file": 16, "line": 61, "parent": 48}, {"command": 7, "file": 0, "line": 51, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 2, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIAWIDGETS_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Desktop/New folder (2)/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/VideoEditor_autogen/include"}, {"backtrace": 50, "path": "C:/Users/<USER>/Desktop/New folder (2)/src"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "dependencies": [{"id": "VideoEditor_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "VideoEditor_autogen::@6890427a1f51a3e7e1df"}], "id": "VideoEditor::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6MultimediaWidgets.a", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Multimedia.a", "role": "libraries"}, {"backtrace": 8, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 18, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 23, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 28, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 28, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 38, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 39, "fragment": "-lshell32", "role": "libraries"}, {"backtrace": 49, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 49, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 49, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 49, "fragment": "-ld3d12", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "VideoEditor", "nameOnDisk": "VideoEditor.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [6, 7, 8, 9]}, {"name": "", "sourceIndexes": [10]}, {"name": "CMake Rules", "sourceIndexes": [11]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/VideoEditor_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/videoplayer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/timeline.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/videoeditor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/videoplayer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/timeline.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/videoeditor.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/VideoEditor_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/VideoEditor_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}