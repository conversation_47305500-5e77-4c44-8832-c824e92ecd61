#include "mainwindow.h"
#include "videoplayer.h"
#include "timeline.h"
#include "videoeditor.h"
#include <QApplication>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_videoSplitter(nullptr)
    , m_videoPlayer(nullptr)
    , m_timeline(nullptr)
    , m_videoEditor(nullptr)
    , m_controlsWidget(nullptr)
    , m_playButton(nullptr)
    , m_pauseButton(nullptr)
    , m_stopButton(nullptr)
    , m_positionSlider(nullptr)
    , m_timeLabel(nullptr)
    , m_durationLabel(nullptr)
    , m_menuBar(nullptr)
    , m_toolBar(nullptr)
    , m_statusBar(nullptr)
    , m_progressBar(nullptr)
    , m_isVideoLoaded(false)
{
    setWindowTitle("Video Editor");
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    connectSignals();
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);
    
    // Create main splitter (horizontal)
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(m_mainSplitter);
    
    // Create video area splitter (vertical)
    m_videoSplitter = new QSplitter(Qt::Vertical, this);
    m_mainSplitter->addWidget(m_videoSplitter);
    
    // Create video player
    m_videoPlayer = new VideoPlayer(this);
    m_videoSplitter->addWidget(m_videoPlayer);
    
    // Create timeline
    m_timeline = new Timeline(this);
    m_videoSplitter->addWidget(m_timeline);
    
    // Create video editor panel
    m_videoEditor = new VideoEditor(this);
    m_mainSplitter->addWidget(m_videoEditor);
    
    // Set splitter proportions
    m_mainSplitter->setSizes({800, 400});
    m_videoSplitter->setSizes({500, 200});
    
    // Create controls widget
    createControlsWidget();
    mainLayout->addWidget(m_controlsWidget);
}

void MainWindow::createControlsWidget()
{
    m_controlsWidget = new QWidget(this);
    m_controlsWidget->setMaximumHeight(80);
    m_controlsWidget->setMinimumHeight(80);
    
    QHBoxLayout *controlsLayout = new QHBoxLayout(m_controlsWidget);
    controlsLayout->setContentsMargins(10, 10, 10, 10);
    
    // Playback controls
    m_playButton = new QPushButton("▶", this);
    m_playButton->setFixedSize(40, 40);
    m_playButton->setToolTip("Play");
    
    m_pauseButton = new QPushButton("⏸", this);
    m_pauseButton->setFixedSize(40, 40);
    m_pauseButton->setToolTip("Pause");
    m_pauseButton->setEnabled(false);
    
    m_stopButton = new QPushButton("⏹", this);
    m_stopButton->setFixedSize(40, 40);
    m_stopButton->setToolTip("Stop");
    m_stopButton->setEnabled(false);
    
    controlsLayout->addWidget(m_playButton);
    controlsLayout->addWidget(m_pauseButton);
    controlsLayout->addWidget(m_stopButton);
    
    controlsLayout->addSpacing(20);
    
    // Time labels
    m_timeLabel = new QLabel("00:00", this);
    m_timeLabel->setMinimumWidth(50);
    controlsLayout->addWidget(m_timeLabel);
    
    // Position slider
    m_positionSlider = new QSlider(Qt::Horizontal, this);
    m_positionSlider->setEnabled(false);
    controlsLayout->addWidget(m_positionSlider);
    
    m_durationLabel = new QLabel("00:00", this);
    m_durationLabel->setMinimumWidth(50);
    controlsLayout->addWidget(m_durationLabel);
}

void MainWindow::setupMenuBar()
{
    m_menuBar = menuBar();
    
    // File menu
    QMenu *fileMenu = m_menuBar->addMenu("&File");
    
    m_openAction = new QAction("&Open Video...", this);
    m_openAction->setShortcut(QKeySequence::Open);
    m_openAction->setToolTip("Open a video file");
    fileMenu->addAction(m_openAction);
    
    fileMenu->addSeparator();
    
    m_saveAction = new QAction("&Save Project", this);
    m_saveAction->setShortcut(QKeySequence::Save);
    m_saveAction->setEnabled(false);
    fileMenu->addAction(m_saveAction);
    
    m_exportAction = new QAction("&Export Video...", this);
    m_exportAction->setShortcut(QKeySequence("Ctrl+E"));
    m_exportAction->setEnabled(false);
    fileMenu->addAction(m_exportAction);
    
    fileMenu->addSeparator();
    
    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    fileMenu->addAction(m_exitAction);
    
    // Help menu
    QMenu *helpMenu = m_menuBar->addMenu("&Help");
    m_aboutAction = new QAction("&About", this);
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupToolBar()
{
    m_toolBar = addToolBar("Main");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
    
    m_toolBar->addAction(m_openAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_saveAction);
    m_toolBar->addAction(m_exportAction);
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    m_statusBar->showMessage("Ready");
    
    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    m_statusBar->addPermanentWidget(m_progressBar);
}

void MainWindow::connectSignals()
{
    // Menu actions
    connect(m_openAction, &QAction::triggered, this, &MainWindow::openFile);
    connect(m_saveAction, &QAction::triggered, this, &MainWindow::saveProject);
    connect(m_exportAction, &QAction::triggered, this, &MainWindow::exportVideo);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::about);
    
    // Control buttons
    connect(m_playButton, &QPushButton::clicked, m_videoPlayer, &VideoPlayer::play);
    connect(m_pauseButton, &QPushButton::clicked, m_videoPlayer, &VideoPlayer::pause);
    connect(m_stopButton, &QPushButton::clicked, m_videoPlayer, &VideoPlayer::stop);
    
    // Video player signals
    connect(m_videoPlayer, &VideoPlayer::videoLoaded, this, &MainWindow::onVideoLoaded);
    connect(m_videoPlayer, &VideoPlayer::errorOccurred, this, &MainWindow::onVideoError);
    connect(m_videoPlayer, &VideoPlayer::positionChanged, this, &MainWindow::onPositionChanged);
    connect(m_videoPlayer, &VideoPlayer::durationChanged, this, &MainWindow::onDurationChanged);
    
    // Position slider
    connect(m_positionSlider, &QSlider::sliderPressed, [this]() {
        m_videoPlayer->setSliderPressed(true);
    });
    connect(m_positionSlider, &QSlider::sliderReleased, [this]() {
        m_videoPlayer->setSliderPressed(false);
        m_videoPlayer->setPosition(m_positionSlider->value());
    });
    
    // Timeline integration
    connect(m_timeline, &Timeline::positionChanged, m_videoPlayer, &VideoPlayer::setPosition);
    connect(m_videoPlayer, &VideoPlayer::positionChanged, m_timeline, &Timeline::setPosition);
    connect(m_videoPlayer, &VideoPlayer::durationChanged, m_timeline, &Timeline::setDuration);
}

void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Video File",
        "",
        "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;All Files (*)");
    
    if (!fileName.isEmpty()) {
        m_statusBar->showMessage("Loading video...");
        m_progressBar->setVisible(true);
        m_videoPlayer->loadVideo(fileName);
    }
}

void MainWindow::saveProject()
{
    // TODO: Implement project saving
    QMessageBox::information(this, "Save Project", "Project saving functionality will be implemented in a future version.");
}

void MainWindow::exportVideo()
{
    // TODO: Implement video export
    QMessageBox::information(this, "Export Video", "Video export functionality will be implemented in a future version.");
}

void MainWindow::about()
{
    QMessageBox::about(this, "About Video Editor",
        "Video Editor v1.0.0\n\n"
        "A basic video editing application built with Qt6 and C++.\n\n"
        "Features:\n"
        "• Video file loading and playback\n"
        "• Timeline scrubbing\n"
        "• Basic editing operations\n\n"
        "Built with Qt " QT_VERSION_STR);
}

void MainWindow::onVideoLoaded(const QString &fileName)
{
    m_currentFile = fileName;
    m_isVideoLoaded = true;
    
    // Enable controls
    m_playButton->setEnabled(true);
    m_pauseButton->setEnabled(true);
    m_stopButton->setEnabled(true);
    m_positionSlider->setEnabled(true);
    m_saveAction->setEnabled(true);
    m_exportAction->setEnabled(true);
    
    m_statusBar->showMessage(QString("Loaded: %1").arg(QFileInfo(fileName).fileName()));
    m_progressBar->setVisible(false);
    
    setWindowTitle(QString("Video Editor - %1").arg(QFileInfo(fileName).fileName()));
}

void MainWindow::onVideoError(const QString &error)
{
    QMessageBox::critical(this, "Video Error", error);
    m_statusBar->showMessage("Error loading video");
    m_progressBar->setVisible(false);
}

void MainWindow::onPositionChanged(qint64 position)
{
    if (!m_positionSlider->isSliderDown()) {
        m_positionSlider->setValue(position);
    }
    
    // Update time label
    int seconds = position / 1000;
    int minutes = seconds / 60;
    seconds %= 60;
    m_timeLabel->setText(QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0')));
}

void MainWindow::onDurationChanged(qint64 duration)
{
    m_positionSlider->setRange(0, duration);
    
    // Update duration label
    int seconds = duration / 1000;
    int minutes = seconds / 60;
    seconds %= 60;
    m_durationLabel->setText(QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0')));
}
