#include "timeline.h"
#include <QApplication>
#include <QDebug>
#include <cmath>

// TimelineWidget Implementation
TimelineWidget::TimelineWidget(QWidget *parent)
    : QWidget(parent)
    , m_duration(0)
    , m_position(0)
    , m_zoomLevel(1.0)
    , m_scrollOffset(0)
    , m_rulerHeight(30)
    , m_trackHeight(60)
    , m_playheadWidth(2)
    , m_dragging(false)
    , m_selecting(false)
    , m_rubberBand(nullptr)
    , m_backgroundColor(QColor(40, 40, 40))
    , m_rulerColor(QColor(60, 60, 60))
    , m_playheadColor(QColor(255, 0, 0))
    , m_selectionColor(QColor(100, 150, 255, 100))
    , m_gridColor(QColor(80, 80, 80))
{
    setMinimumHeight(m_rulerHeight + m_trackHeight + 20);
    setMouseTracking(true);
    setFocusPolicy(Qt::StrongFocus);
}

void TimelineWidget::setDuration(qint64 duration)
{
    m_duration = duration;
    update();
}

void TimelineWidget::setPosition(qint64 position)
{
    if (m_position != position) {
        m_position = position;
        update();
    }
}

void TimelineWidget::addClip(const VideoClip &clip)
{
    m_clips.append(clip);
    update();
}

void TimelineWidget::removeSelectedClips()
{
    for (int i = m_clips.size() - 1; i >= 0; --i) {
        if (m_clips[i].selected) {
            m_clips.removeAt(i);
        }
    }
    update();
    emit selectionChanged();
}

void TimelineWidget::clearClips()
{
    m_clips.clear();
    update();
}

void TimelineWidget::setZoomLevel(double zoom)
{
    m_zoomLevel = qMax(0.1, qMin(10.0, zoom));
    update();
}

QList<VideoClip> TimelineWidget::selectedClips() const
{
    QList<VideoClip> selected;
    for (const VideoClip &clip : m_clips) {
        if (clip.selected) {
            selected.append(clip);
        }
    }
    return selected;
}

void TimelineWidget::selectClip(int index)
{
    if (index >= 0 && index < m_clips.size()) {
        clearSelection();
        m_clips[index].selected = true;
        update();
        emit clipSelected(index);
        emit selectionChanged();
    }
}

void TimelineWidget::clearSelection()
{
    bool hadSelection = false;
    for (VideoClip &clip : m_clips) {
        if (clip.selected) {
            clip.selected = false;
            hadSelection = true;
        }
    }
    if (hadSelection) {
        update();
        emit selectionChanged();
    }
}

void TimelineWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Fill background
    painter.fillRect(rect(), m_backgroundColor);
    
    // Draw components
    drawTimeRuler(painter);
    drawClips(painter);
    drawPlayhead(painter);
    drawSelection(painter);
}

void TimelineWidget::drawTimeRuler(QPainter &painter)
{
    QRect rulerRect(0, 0, width(), m_rulerHeight);
    painter.fillRect(rulerRect, m_rulerColor);
    
    if (m_duration <= 0) return;
    
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 8));
    
    // Calculate time intervals
    double pixelsPerMs = (width() * m_zoomLevel) / double(m_duration);
    int intervalMs = 1000; // Start with 1 second
    
    // Adjust interval based on zoom
    while (intervalMs * pixelsPerMs < 50) {
        intervalMs *= 2;
    }
    while (intervalMs * pixelsPerMs > 200) {
        intervalMs /= 2;
    }
    
    // Draw time markers
    for (qint64 time = 0; time <= m_duration; time += intervalMs) {
        int x = timeToPixel(time);
        if (x >= 0 && x < width()) {
            painter.drawLine(x, 0, x, m_rulerHeight);
            
            QString timeText = formatTime(time);
            QRect textRect(x + 2, 2, 100, m_rulerHeight - 4);
            painter.drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, timeText);
        }
    }
}

void TimelineWidget::drawClips(QPainter &painter)
{
    QRect trackRect(0, m_rulerHeight, width(), m_trackHeight);
    painter.fillRect(trackRect, m_backgroundColor.lighter(110));
    
    // Draw grid lines
    painter.setPen(QPen(m_gridColor, 1, Qt::DotLine));
    for (int i = 0; i < 5; ++i) {
        int y = m_rulerHeight + (i + 1) * (m_trackHeight / 6);
        painter.drawLine(0, y, width(), y);
    }
    
    // Draw clips
    for (int i = 0; i < m_clips.size(); ++i) {
        const VideoClip &clip = m_clips[i];
        
        int startX = timeToPixel(clip.startTime);
        int endX = timeToPixel(clip.endTime);
        int clipWidth = endX - startX;
        
        if (startX < width() && endX > 0 && clipWidth > 0) {
            QRect clipRect(startX, m_rulerHeight + 5, clipWidth, m_trackHeight - 10);
            
            // Clip background
            QColor clipColor = clip.color;
            if (clip.selected) {
                clipColor = clipColor.lighter(150);
            }
            painter.fillRect(clipRect, clipColor);
            
            // Clip border
            painter.setPen(QPen(clip.selected ? Qt::yellow : Qt::white, clip.selected ? 2 : 1));
            painter.drawRect(clipRect);
            
            // Clip text
            if (clipWidth > 50) {
                painter.setPen(Qt::white);
                painter.setFont(QFont("Arial", 9, QFont::Bold));
                painter.drawText(clipRect, Qt::AlignCenter, clip.name);
            }
        }
    }
}

void TimelineWidget::drawPlayhead(QPainter &painter)
{
    if (m_duration <= 0) return;
    
    int x = timeToPixel(m_position);
    if (x >= 0 && x < width()) {
        painter.setPen(QPen(m_playheadColor, m_playheadWidth));
        painter.drawLine(x, 0, x, height());
        
        // Draw playhead handle
        QPolygon triangle;
        triangle << QPoint(x - 5, 0) << QPoint(x + 5, 0) << QPoint(x, 10);
        painter.setBrush(m_playheadColor);
        painter.drawPolygon(triangle);
    }
}

void TimelineWidget::drawSelection(QPainter &painter)
{
    if (m_rubberBand && m_rubberBand->isVisible()) {
        painter.setPen(QPen(m_selectionColor.darker(), 1));
        painter.fillRect(m_rubberBand->geometry(), m_selectionColor);
    }
}

void TimelineWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragStart = event->pos();
        m_lastMousePos = event->pos();
        
        // Check if clicking on a clip
        int clipIndex = findClipAt(event->pos());
        if (clipIndex >= 0) {
            if (!event->modifiers().testFlag(Qt::ControlModifier)) {
                clearSelection();
            }
            m_clips[clipIndex].selected = !m_clips[clipIndex].selected;
            m_dragging = true;
            update();
            emit clipSelected(clipIndex);
            emit selectionChanged();
        } else {
            // Start selection or seek
            if (event->pos().y() < m_rulerHeight) {
                // Seek in ruler area
                qint64 newPosition = pixelToTime(event->pos().x());
                emit positionChanged(newPosition);
            } else {
                // Start rubber band selection
                clearSelection();
                m_selecting = true;
                if (!m_rubberBand) {
                    m_rubberBand = new QRubberBand(QRubberBand::Rectangle, this);
                }
                m_rubberBand->setGeometry(QRect(m_dragStart, QSize()));
                m_rubberBand->show();
            }
        }
    }
}

void TimelineWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_selecting && m_rubberBand) {
        m_rubberBand->setGeometry(QRect(m_dragStart, event->pos()).normalized());
    } else if (m_dragging) {
        // Handle clip dragging (simplified)
        update();
    }
    
    m_lastMousePos = event->pos();
}

void TimelineWidget::mouseReleaseEvent(QMouseEvent *event)
{
    Q_UNUSED(event)
    if (m_selecting && m_rubberBand) {
        // Select clips within rubber band
        QRect selectionRect = m_rubberBand->geometry();
        for (int i = 0; i < m_clips.size(); ++i) {
            int startX = timeToPixel(m_clips[i].startTime);
            int endX = timeToPixel(m_clips[i].endTime);
            QRect clipRect(startX, m_rulerHeight + 5, endX - startX, m_trackHeight - 10);
            
            if (selectionRect.intersects(clipRect)) {
                m_clips[i].selected = true;
            }
        }
        
        m_rubberBand->hide();
        m_selecting = false;
        update();
        emit selectionChanged();
    }
    
    m_dragging = false;
}

void TimelineWidget::mouseDoubleClickEvent(QMouseEvent *event)
{
    int clipIndex = findClipAt(event->pos());
    if (clipIndex >= 0) {
        emit clipDoubleClicked(clipIndex);
    }
}

void TimelineWidget::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        // Zoom
        double zoomFactor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
        setZoomLevel(m_zoomLevel * zoomFactor);
    } else {
        // Scroll
        m_scrollOffset += event->angleDelta().y() / 8;
        update();
    }
}

void TimelineWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updateScrollBars();
}

qint64 TimelineWidget::pixelToTime(int pixel) const
{
    if (m_duration <= 0) return 0;
    return (pixel * m_duration) / (width() * m_zoomLevel);
}

int TimelineWidget::timeToPixel(qint64 time) const
{
    if (m_duration <= 0) return 0;
    return (time * width() * m_zoomLevel) / m_duration;
}

QString TimelineWidget::formatTime(qint64 milliseconds) const
{
    int seconds = milliseconds / 1000;
    int minutes = seconds / 60;
    seconds %= 60;
    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

int TimelineWidget::findClipAt(const QPoint &pos) const
{
    if (pos.y() < m_rulerHeight || pos.y() > m_rulerHeight + m_trackHeight) {
        return -1;
    }
    
    qint64 time = pixelToTime(pos.x());
    for (int i = 0; i < m_clips.size(); ++i) {
        if (time >= m_clips[i].startTime && time <= m_clips[i].endTime) {
            return i;
        }
    }
    return -1;
}

void TimelineWidget::updateScrollBars()
{
    // TODO: Implement scroll bar updates
}

// Timeline Implementation
Timeline::Timeline(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_controlsLayout(nullptr)
    , m_timelineWidget(nullptr)
    , m_horizontalScrollBar(nullptr)
    , m_currentDuration(0)
    , m_currentPosition(0)
{
    setupUI();
    setupControls();
}

void Timeline::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // Create timeline widget
    m_timelineWidget = new TimelineWidget(this);
    m_mainLayout->addWidget(m_timelineWidget);
    
    // Create horizontal scroll bar
    m_horizontalScrollBar = new QScrollBar(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_horizontalScrollBar);
    
    // Connect timeline signals
    connect(m_timelineWidget, &TimelineWidget::positionChanged,
            this, &Timeline::onTimelinePositionChanged);
    connect(m_timelineWidget, &TimelineWidget::clipSelected,
            this, &Timeline::onClipSelected);
}

void Timeline::setupControls()
{
    m_controlsLayout = new QHBoxLayout();
    
    // Zoom controls
    m_zoomLabel = new QLabel("Zoom:", this);
    m_zoomSpinBox = new QSpinBox(this);
    m_zoomSpinBox->setRange(10, 1000);
    m_zoomSpinBox->setValue(100);
    m_zoomSpinBox->setSuffix("%");
    
    // Edit controls
    m_splitButton = new QPushButton("Split", this);
    m_splitButton->setEnabled(false);
    m_deleteButton = new QPushButton("Delete", this);
    m_deleteButton->setEnabled(false);
    m_clearButton = new QPushButton("Clear All", this);
    
    m_controlsLayout->addWidget(m_zoomLabel);
    m_controlsLayout->addWidget(m_zoomSpinBox);
    m_controlsLayout->addSpacing(20);
    m_controlsLayout->addWidget(m_splitButton);
    m_controlsLayout->addWidget(m_deleteButton);
    m_controlsLayout->addWidget(m_clearButton);
    m_controlsLayout->addStretch();
    
    m_mainLayout->addLayout(m_controlsLayout);
    
    // Connect control signals
    connect(m_zoomSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &Timeline::onZoomChanged);
    connect(m_splitButton, &QPushButton::clicked, [this]() {
        splitClipAt(m_currentPosition);
    });
    connect(m_deleteButton, &QPushButton::clicked, [this]() {
        removeSelectedClips();
    });
    connect(m_clearButton, &QPushButton::clicked, [this]() {
        clearTimeline();
    });
}

void Timeline::setDuration(qint64 duration)
{
    m_currentDuration = duration;
    m_timelineWidget->setDuration(duration);
    
    // Add a default clip spanning the entire duration
    if (duration > 0) {
        clearTimeline();
        addClip(0, duration, "Main Clip");
    }
}

void Timeline::setPosition(qint64 position)
{
    m_currentPosition = position;
    m_timelineWidget->setPosition(position);
}

void Timeline::addClip(qint64 startTime, qint64 endTime, const QString &name)
{
    VideoClip clip(startTime, endTime, name);
    clip.color = QColor(70, 130, 180); // Steel blue
    m_timelineWidget->addClip(clip);
    emit clipModified();
}

void Timeline::splitClipAt(qint64 position)
{
    // Find clips that contain the split position
    QList<VideoClip> clipsToSplit;
    QList<int> indicesToRemove;

    // Note: This is a simplified implementation
    // In a real application, you would need proper access to timeline clips
    // For now, we'll clear and re-add clips as a demonstration

    // Clear existing clips and add a split demonstration
    m_timelineWidget->clearClips();

    // Add example split clips
    if (m_currentDuration > 0) {
        if (position > 0) {
            VideoClip firstPart(0, position, "Split Part 1");
            firstPart.color = QColor(70, 130, 180);
            m_timelineWidget->addClip(firstPart);
        }

        if (position < m_currentDuration) {
            VideoClip secondPart(position, m_currentDuration, "Split Part 2");
            secondPart.color = QColor(180, 130, 70);
            m_timelineWidget->addClip(secondPart);
        }
    }



    emit clipModified();
}

void Timeline::removeSelectedClips()
{
    m_timelineWidget->removeSelectedClips();
    m_deleteButton->setEnabled(false);
    emit clipModified();
}

void Timeline::clearTimeline()
{
    m_timelineWidget->clearClips();
    m_deleteButton->setEnabled(false);
    emit clipModified();
}

void Timeline::onZoomChanged(int value)
{
    double zoom = value / 100.0;
    m_timelineWidget->setZoomLevel(zoom);
}

void Timeline::onTimelinePositionChanged(qint64 position)
{
    m_currentPosition = position;
    emit positionChanged(position);
}

void Timeline::onClipSelected(int index)
{
    Q_UNUSED(index)
    m_deleteButton->setEnabled(true);
    m_splitButton->setEnabled(true);
}
