@echo off
REM Create portable VideoEditor package with all dependencies
REM This creates a folder that can be zipped and distributed

echo ========================================
echo VideoEditor Portable Package Creator
echo ========================================
echo.

REM Set Qt path (adjust if needed)
set QT_DIR=C:\Qt\6.9.1\mingw_64
set PATH=%QT_DIR%\bin;%PATH%

REM Check if executable exists
set EXE_PATH=build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\VideoEditor.exe
if not exist "%EXE_PATH%" (
    echo ERROR: VideoEditor.exe not found at %EXE_PATH%
    echo Please build the project first in Qt Creator.
    pause
    exit /b 1
)

REM Create portable directory
set PORTABLE_DIR=VideoEditor_Portable
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

echo Copying executable...
copy "%EXE_PATH%" "%PORTABLE_DIR%\"

echo Deploying Qt dependencies...
REM Use windeployqt to automatically copy all required DLLs
windeployqt.exe "%PORTABLE_DIR%\VideoEditor.exe" --qmldir . --compiler-runtime

echo Copying documentation...
copy "README.md" "%PORTABLE_DIR%\"
copy "LICENSE.txt" "%PORTABLE_DIR%\"

REM Create a run script
echo @echo off > "%PORTABLE_DIR%\Run_VideoEditor.bat"
echo start VideoEditor.exe >> "%PORTABLE_DIR%\Run_VideoEditor.bat"

echo.
echo ========================================
echo SUCCESS! Portable package created!
echo ========================================
echo.
echo Folder: %PORTABLE_DIR%
echo.
echo You can now:
echo 1. Test by running: %PORTABLE_DIR%\VideoEditor.exe
echo 2. Zip the folder for distribution
echo 3. Copy to any Windows computer and run
echo.
echo The portable package includes all Qt DLLs
echo and can run without installation!
echo.

REM Test the portable version
echo Testing portable version...
echo.
set /p choice="Do you want to test the portable version now? (y/n): "
if /i "%choice%"=="y" (
    start "" "%PORTABLE_DIR%\VideoEditor.exe"
    echo.
    echo If the application starts successfully, the portable package is ready!
)

pause
