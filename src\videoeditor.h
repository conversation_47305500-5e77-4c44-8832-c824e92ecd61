#ifndef VIDEOEDITOR_H
#define VIDEOEDITOR_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QListWidget>
#include <QProgressBar>
#include <QTextEdit>
#include <QSplitter>
#include <QFileInfo>
#include <QFileDialog>
#include <QMessageBox>
#include <QProcess>
#include <QTimer>
#include <QThread>
#include <QMutex>

// Forward declarations
class VideoExportWorker;

class VideoEditor : public QWidget
{
    Q_OBJECT

public:
    explicit VideoEditor(QWidget *parent = nullptr);
    
    // File operations
    void addVideoFile(const QString &filePath);
    void removeSelectedFiles();
    void clearFileList();
    
    // Project operations
    void newProject();
    void loadProject(const QString &projectPath);
    void saveProject(const QString &projectPath);
    
    // Export operations
    void exportVideo(const QString &outputPath);

signals:
    void fileAdded(const QString &filePath);
    void fileRemoved(const QString &filePath);
    void exportStarted();
    void exportFinished(bool success);
    void exportProgress(int percentage);

private slots:
    void onAddFileClicked();
    void onRemoveFileClicked();
    void onClearFilesClicked();
    void onFileSelectionChanged();
    void onExportClicked();
    void onQualityChanged(int index);
    void onResolutionChanged(int index);
    void onFormatChanged(int index);
    void onExportProgress(int percentage);
    void onExportFinished(bool success, const QString &message);

private:
    void setupUI();
    void setupFilePanel();
    void setupEditingPanel();
    void setupExportPanel();
    void setupPropertiesPanel();
    void updateFileInfo();
    void updateExportSettings();
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QSplitter *m_splitter;
    
    // File management panel
    QGroupBox *m_fileGroup;
    QVBoxLayout *m_fileLayout;
    QListWidget *m_fileList;
    QHBoxLayout *m_fileButtonsLayout;
    QPushButton *m_addFileButton;
    QPushButton *m_removeFileButton;
    QPushButton *m_clearFilesButton;
    
    // Editing panel
    QGroupBox *m_editingGroup;
    QVBoxLayout *m_editingLayout;
    QPushButton *m_cutButton;
    QPushButton *m_copyButton;
    QPushButton *m_pasteButton;
    QPushButton *m_trimButton;
    QPushButton *m_splitButton;
    QPushButton *m_mergeButton;
    
    // Export panel
    QGroupBox *m_exportGroup;
    QVBoxLayout *m_exportLayout;
    QHBoxLayout *m_qualityLayout;
    QLabel *m_qualityLabel;
    QComboBox *m_qualityCombo;
    QHBoxLayout *m_resolutionLayout;
    QLabel *m_resolutionLabel;
    QComboBox *m_resolutionCombo;
    QHBoxLayout *m_formatLayout;
    QLabel *m_formatLabel;
    QComboBox *m_formatCombo;
    QHBoxLayout *m_bitrateLayout;
    QLabel *m_bitrateLabel;
    QSpinBox *m_bitrateSpinBox;
    QPushButton *m_exportButton;
    QProgressBar *m_exportProgress;
    QLabel *m_exportStatusLabel;
    
    // Properties panel
    QGroupBox *m_propertiesGroup;
    QVBoxLayout *m_propertiesLayout;
    QTextEdit *m_propertiesText;
    
    // Current state
    QStringList m_videoFiles;
    QString m_currentProject;
    bool m_projectModified;

    // Export worker
    QThread *m_exportThread;
    VideoExportWorker *m_exportWorker;
    
    // Export settings
    struct ExportSettings {
        QString quality;
        QString resolution;
        int bitrate;
        QString format;
        
        ExportSettings() : quality("High"), resolution("1920x1080"), bitrate(5000), format("mp4") {}
    } m_exportSettings;
};

// Video Export Worker Class
class VideoExportWorker : public QObject
{
    Q_OBJECT

public:
    struct ExportJob {
        QStringList inputFiles;
        QString outputPath;
        QString quality;
        QString resolution;
        QString format;
        int bitrate;
    };

public slots:
    void exportVideo(const ExportJob &job);

signals:
    void progressChanged(int percentage);
    void finished(bool success, const QString &message);

private:
    QString buildFFmpegCommand(const ExportJob &job);
    bool isFFmpegAvailable();
};

#endif // VIDEOEDITOR_H
