@echo off
REM Manual DLL copy script for VideoEditor
REM Adjust QT_DIR to match your Qt installation

set QT_DIR=C:\Qt\6.9.1\mingw_64
set MINGW_DIR=C:\Qt\Tools\mingw1310_64

REM Find executable directory
set TARGET_DIR=
if exist "build-cmake\VideoEditor.exe" set TARGET_DIR=build-cmake
if exist "build-cmake\Debug\VideoEditor.exe" set TARGET_DIR=build-cmake\Debug
if exist "build-cmake\Release\VideoEditor.exe" set TARGET_DIR=build-cmake\Release

if "%TARGET_DIR%"=="" (
    echo Error: VideoEditor.exe not found
    pause
    exit /b 1
)

echo Copying Qt DLLs to %TARGET_DIR%...

REM Copy essential Qt DLLs
copy "%QT_DIR%\bin\Qt6Core.dll" "%TARGET_DIR%\"
copy "%QT_DIR%\bin\Qt6Gui.dll" "%TARGET_DIR%\"
copy "%QT_DIR%\bin\Qt6Widgets.dll" "%TARGET_DIR%\"
copy "%QT_DIR%\bin\Qt6Multimedia.dll" "%TARGET_DIR%\"
copy "%QT_DIR%\bin\Qt6MultimediaWidgets.dll" "%TARGET_DIR%\"
copy "%QT_DIR%\bin\Qt6Network.dll" "%TARGET_DIR%\"

REM Copy MinGW runtime DLLs
copy "%MINGW_DIR%\bin\libgcc_s_seh-1.dll" "%TARGET_DIR%\"
copy "%MINGW_DIR%\bin\libstdc++-6.dll" "%TARGET_DIR%\"
copy "%MINGW_DIR%\bin\libwinpthread-1.dll" "%TARGET_DIR%\"

REM Create platforms directory and copy platform plugin
mkdir "%TARGET_DIR%\platforms" 2>nul
copy "%QT_DIR%\plugins\platforms\qwindows.dll" "%TARGET_DIR%\platforms\"

echo.
echo DLL copy complete! Try running VideoEditor.exe now.
pause
