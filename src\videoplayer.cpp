#include "videoplayer.h"
#include <QApplication>
#include <QDebug>

VideoPlayer::VideoPlayer(QWidget *parent)
    : QWidget(parent)
    , m_mediaPlayer(nullptr)
    , m_videoWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_videoLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_sliderPressed(false)
    , m_videoLoaded(false)
    , m_positionTimer(nullptr)
{
    setupUI();
    setupMediaPlayer();
    connectSignals();
}

VideoPlayer::~VideoPlayer()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->stop();
    }
}

void VideoPlayer::setupUI()
{
    setMinimumSize(640, 360);
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // Create video widget
    m_videoWidget = new QVideoWidget(this);
    m_videoWidget->setMinimumSize(640, 360);
    m_videoWidget->setStyleSheet("background-color: black; border: 1px solid #555;");
    m_mainLayout->addWidget(m_videoWidget);
    
    // Create status label
    m_statusLabel = new QLabel("No video loaded", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("color: #888; font-size: 12px; padding: 5px;");
    m_mainLayout->addWidget(m_statusLabel);
    
    // Create placeholder label (shown when no video is loaded)
    m_videoLabel = new QLabel("Drop a video file here or use File > Open Video", this);
    m_videoLabel->setAlignment(Qt::AlignCenter);
    m_videoLabel->setStyleSheet(
        "color: #888; "
        "font-size: 16px; "
        "border: 2px dashed #555; "
        "padding: 20px; "
        "background-color: #2a2a2a;"
    );
    m_videoLabel->setMinimumHeight(200);
    
    // Initially show the placeholder
    m_videoWidget->hide();
    m_mainLayout->insertWidget(0, m_videoLabel);
}

void VideoPlayer::setupMediaPlayer()
{
    m_mediaPlayer = new QMediaPlayer(this);
    m_mediaPlayer->setVideoOutput(m_videoWidget);

    // Create and set audio output
    QAudioOutput *audioOutput = new QAudioOutput(this);
    m_mediaPlayer->setAudioOutput(audioOutput);

    // Create position update timer
    m_positionTimer = new QTimer(this);
    m_positionTimer->setInterval(100); // Update every 100ms
    connect(m_positionTimer, &QTimer::timeout, [this]() {
        if (m_mediaPlayer && !m_sliderPressed) {
            emit positionChanged(m_mediaPlayer->position());
        }
    });
}

void VideoPlayer::connectSignals()
{
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged,
            this, &VideoPlayer::onMediaStatusChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged,
            this, &VideoPlayer::onPlaybackStateChanged);
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged,
            this, &VideoPlayer::onPositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged,
            this, &VideoPlayer::onDurationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::errorOccurred,
            this, &VideoPlayer::onErrorOccurred);
}

void VideoPlayer::loadVideo(const QString &filePath)
{
    if (filePath.isEmpty()) {
        return;
    }
    
    m_currentFile = filePath;
    QUrl url = QUrl::fromLocalFile(filePath);
    
    m_statusLabel->setText("Loading video...");
    m_mediaPlayer->setSource(url);
    
    // Hide placeholder and show video widget
    if (m_videoLabel->isVisible()) {
        m_videoLabel->hide();
        m_videoWidget->show();
    }
}

void VideoPlayer::play()
{
    if (m_mediaPlayer && m_videoLoaded) {
        m_mediaPlayer->play();
        m_positionTimer->start();
    }
}

void VideoPlayer::pause()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->pause();
        m_positionTimer->stop();
    }
}

void VideoPlayer::stop()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->stop();
        m_positionTimer->stop();
    }
}

void VideoPlayer::setPosition(qint64 position)
{
    if (m_mediaPlayer && m_videoLoaded) {
        m_mediaPlayer->setPosition(position);
    }
}

void VideoPlayer::setVolume(int volume)
{
    if (m_mediaPlayer) {
        // Convert percentage to float (0.0 - 1.0)
        float volumeLevel = volume / 100.0f;

        // Create audio output if it doesn't exist
        if (!m_mediaPlayer->audioOutput()) {
            QAudioOutput *audioOutput = new QAudioOutput(this);
            m_mediaPlayer->setAudioOutput(audioOutput);
        }

        if (m_mediaPlayer->audioOutput()) {
            m_mediaPlayer->audioOutput()->setVolume(volumeLevel);
        }
    }
}

bool VideoPlayer::isPlaying() const
{
    return m_mediaPlayer && m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState;
}

qint64 VideoPlayer::position() const
{
    return m_mediaPlayer ? m_mediaPlayer->position() : 0;
}

qint64 VideoPlayer::duration() const
{
    return m_mediaPlayer ? m_mediaPlayer->duration() : 0;
}

void VideoPlayer::setSliderPressed(bool pressed)
{
    m_sliderPressed = pressed;
}

void VideoPlayer::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    switch (status) {
    case QMediaPlayer::LoadingMedia:
        m_statusLabel->setText("Loading media...");
        break;
    case QMediaPlayer::LoadedMedia:
        m_statusLabel->setText("Media loaded successfully");
        m_videoLoaded = true;
        emit videoLoaded(m_currentFile);
        break;
    case QMediaPlayer::BufferingMedia:
        m_statusLabel->setText("Buffering...");
        break;
    case QMediaPlayer::BufferedMedia:
        m_statusLabel->setText("Ready to play");
        break;
    case QMediaPlayer::EndOfMedia:
        m_statusLabel->setText("End of media");
        m_positionTimer->stop();
        break;
    case QMediaPlayer::InvalidMedia:
        m_statusLabel->setText("Invalid media");
        m_videoLoaded = false;
        break;
    default:
        break;
    }
}

void VideoPlayer::onPlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    switch (state) {
    case QMediaPlayer::PlayingState:
        m_statusLabel->setText("Playing");
        m_positionTimer->start();
        break;
    case QMediaPlayer::PausedState:
        m_statusLabel->setText("Paused");
        m_positionTimer->stop();
        break;
    case QMediaPlayer::StoppedState:
        m_statusLabel->setText("Stopped");
        m_positionTimer->stop();
        break;
    }
    
    emit playbackStateChanged(state);
}

void VideoPlayer::onPositionChanged(qint64 position)
{
    if (!m_sliderPressed) {
        emit positionChanged(position);
    }
}

void VideoPlayer::onDurationChanged(qint64 duration)
{
    emit durationChanged(duration);
    
    if (duration > 0) {
        QString durationText = formatTime(duration);
        m_statusLabel->setText(QString("Duration: %1").arg(durationText));
    }
}

void VideoPlayer::onErrorOccurred(QMediaPlayer::Error error, const QString &errorString)
{
    QString errorMsg;
    switch (error) {
    case QMediaPlayer::NoError:
        return;
    case QMediaPlayer::ResourceError:
        errorMsg = "Resource error: " + errorString;
        break;
    case QMediaPlayer::FormatError:
        errorMsg = "Format error: " + errorString;
        break;
    case QMediaPlayer::NetworkError:
        errorMsg = "Network error: " + errorString;
        break;
    case QMediaPlayer::AccessDeniedError:
        errorMsg = "Access denied: " + errorString;
        break;
    default:
        errorMsg = "Unknown error: " + errorString;
        break;
    }
    
    m_statusLabel->setText("Error occurred");
    m_videoLoaded = false;
    
    // Show placeholder again on error
    m_videoWidget->hide();
    if (!m_videoLabel->isVisible()) {
        m_mainLayout->insertWidget(0, m_videoLabel);
        m_videoLabel->show();
    }
    
    emit errorOccurred(errorMsg);
}

QString VideoPlayer::formatTime(qint64 milliseconds) const
{
    int seconds = milliseconds / 1000;
    int minutes = seconds / 60;
    int hours = minutes / 60;
    
    seconds %= 60;
    minutes %= 60;
    
    if (hours > 0) {
        return QString("%1:%2:%3")
            .arg(hours, 2, 10, QChar('0'))
            .arg(minutes, 2, 10, QChar('0'))
            .arg(seconds, 2, 10, QChar('0'));
    } else {
        return QString("%1:%2")
            .arg(minutes, 2, 10, QChar('0'))
            .arg(seconds, 2, 10, QChar('0'));
    }
}
