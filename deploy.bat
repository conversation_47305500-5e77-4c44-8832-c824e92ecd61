@echo off
REM Qt Deployment script for VideoEditor
REM This will copy all necessary Qt DLLs to the executable directory

REM Set Qt path (adjust this to your Qt installation)
set QT_DIR=C:\Qt\6.9.1\mingw_64
set PATH=%QT_DIR%\bin;%PATH%

REM Find the executable (check common build directories)
set EXE_PATH=
if exist "build-cmake\VideoEditor.exe" set EXE_PATH=build-cmake\VideoEditor.exe
if exist "build-cmake\Debug\VideoEditor.exe" set EXE_PATH=build-cmake\Debug\VideoEditor.exe
if exist "build-cmake\Release\VideoEditor.exe" set EXE_PATH=build-cmake\Release\VideoEditor.exe

if "%EXE_PATH%"=="" (
    echo Error: VideoEditor.exe not found in build directories
    echo Please build the project first
    pause
    exit /b 1
)

echo Found executable: %EXE_PATH%
echo.
echo Deploying Qt libraries...

REM Use windeployqt to automatically copy all required DLLs
windeployqt.exe "%EXE_PATH%" --qmldir . --compiler-runtime

echo.
echo Deployment complete! You can now run VideoEditor.exe
echo.
pause
