# Puttis VideoEditor

A professional video editing application built with Qt6 and C++ with full export functionality powered by FFmpeg.

## Features

- **Video File Loading**: Support for common video formats (MP4, AVI, MOV, MKV, WMV, FLV, WebM)
- **Video Playback**: Play, pause, stop, and seek functionality
- **Timeline Interface**: Visual timeline with scrubbing support and zoom controls
- **Basic Editing Operations**: 
  - Cut, copy, paste clips
  - Split clips at current position
  - Trim and merge functionality
  - Timeline-based editing
- **Project Management**: File management panel with drag-and-drop support
- **Professional Export**: Full video export with FFmpeg integration
  - Multiple formats: MP4, AVI, MOV, MKV, WebM
  - Quality presets: Ultra, High, Medium, Low
  - Resolution options: 480p to 4K
  - Configurable bitrate settings
- **Modern UI**: Dark theme with professional layout

## Requirements

### System Requirements
- Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- Qt6.2 or later (Qt5.15+ also supported)
- CMake 3.16 or later
- C++17 compatible compiler
- **FFmpeg** (required for video export functionality)

### Qt Modules Required
- Qt6Core
- Qt6Widgets
- Qt6Multimedia
- Qt6MultimediaWidgets

## FFmpeg Installation (Required for Export)

### Windows
1. Download FFmpeg from https://ffmpeg.org/download.html
2. Extract to a folder (e.g., `C:\ffmpeg`)
3. Add `C:\ffmpeg\bin` to your system PATH
4. Restart your computer

### macOS
```bash
# Using Homebrew
brew install ffmpeg
```

### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
```

### Verify Installation
```bash
ffmpeg -version
```

## Building the Project

### Using Qt Creator (Recommended)

1. **Install Qt6**: Download and install Qt6 from [qt.io](https://www.qt.io/download)
   - Make sure to include Qt Multimedia modules during installation

2. **Open Project in Qt Creator**:
   - Launch Qt Creator
   - File → Open File or Project
   - Navigate to the project directory and select `CMakeLists.txt`
   - Qt Creator will automatically configure the project

3. **Configure Build**:
   - Select your Qt6 kit in the project configuration
   - Choose Debug or Release build type
   - Click "Configure Project"

4. **Build and Run**:
   - Press `Ctrl+B` (or Cmd+B on macOS) to build
   - Press `Ctrl+R` (or Cmd+R on macOS) to run

### Command Line Build

```bash
# Create build directory
mkdir build
cd build

# Configure with CMake
cmake .. -DCMAKE_PREFIX_PATH=/path/to/Qt6

# Build
cmake --build .

# Run (Windows)
./VideoEditor.exe

# Run (Linux/macOS)
./VideoEditor
```

### Windows Specific Notes

If you encounter DLL issues on Windows, make sure Qt6 DLLs are in your PATH or copy them to the executable directory:
- Qt6Core.dll
- Qt6Widgets.dll
- Qt6Multimedia.dll
- Qt6MultimediaWidgets.dll

## Usage

### Getting Started

1. **Launch the Application**: Run VideoEditor from Qt Creator or command line
2. **Load a Video**: 
   - Use File → Open Video... or click the toolbar button
   - Select a supported video file
3. **Playback Controls**: Use the play, pause, and stop buttons at the bottom
4. **Timeline Navigation**: Click on the timeline ruler to seek to specific positions

### Basic Editing Workflow

1. **Import Files**: 
   - Use the "Add Files..." button in the Project Files panel
   - Drag and drop video files into the file list

2. **Timeline Operations**:
   - Zoom in/out using the zoom controls or Ctrl+Mouse Wheel
   - Click and drag on the timeline to scrub through video
   - Select clips by clicking on them in the timeline

3. **Editing Operations**:
   - **Split**: Position the playhead and click "Split" to divide a clip
   - **Delete**: Select clips and click "Delete" to remove them
   - **Clear All**: Remove all clips from the timeline

4. **Export Video**:
   - **Select Format**: Choose from MP4, AVI, MOV, MKV, or WebM
   - **Choose Quality**: Ultra (best), High (recommended), Medium, or Low
   - **Set Resolution**: From 480p to 4K (2160p)
   - **Adjust Bitrate**: Automatically set based on quality/resolution
   - **Export**: Click "Export Video..." and choose output location
   - **Monitor Progress**: Watch the progress bar and status updates

## Project Structure

```
VideoEditor/
├── CMakeLists.txt          # Build configuration
├── README.md              # This file
├── src/                   # Source files
│   ├── main.cpp          # Application entry point
│   ├── mainwindow.h/cpp  # Main application window
│   ├── videoplayer.h/cpp # Video playback component
│   ├── timeline.h/cpp    # Timeline and editing interface
│   └── videoeditor.h/cpp # File management and export
└── ui/                   # UI definition files
    └── mainwindow.ui     # Main window layout
```

## Architecture

The application follows a modular design with clear separation of concerns:

- **MainWindow**: Coordinates all components and handles menu/toolbar actions
- **VideoPlayer**: Manages video playback using Qt Multimedia
- **Timeline**: Provides visual timeline interface and editing operations
- **VideoEditor**: Handles file management, project operations, and export settings

## Supported Formats

### Input Formats
- MP4 (H.264, H.265)
- AVI
- MOV (QuickTime)
- MKV (Matroska)
- WMV (Windows Media)
- FLV (Flash Video)
- WebM
- M4V

### Export Formats
- **MP4 (H.264)**: Most compatible, recommended for general use
- **AVI (Xvid)**: Legacy format, good compatibility
- **MOV (H.264)**: Apple QuickTime format
- **MKV (H.264)**: Open source container, excellent quality
- **WebM (VP9)**: Web-optimized format

### Export Quality Settings
- **Ultra**: Highest quality (CRF 18), larger files
- **High**: Excellent quality (CRF 23), balanced size - *Recommended*
- **Medium**: Good quality (CRF 28), smaller files
- **Low**: Basic quality (CRF 32), smallest files

## Known Limitations

This is a basic video editor designed for educational and demonstration purposes:

- No advanced effects or transitions
- Limited audio editing capabilities
- No multi-track editing
- Export requires FFmpeg to be installed and in system PATH
- No undo/redo system
- No keyframe-based editing

## Future Enhancements

Potential improvements for future versions:
- FFmpeg integration for robust video processing
- Multi-track timeline support
- Audio waveform visualization
- Video effects and transitions
- Undo/redo functionality
- Keyframe animation support
- Plugin system for extensions

## Troubleshooting

### Common Issues

1. **Video won't load**: 
   - Ensure the video format is supported
   - Check that Qt Multimedia codecs are installed

2. **Build errors**:
   - Verify Qt6 installation and PATH configuration
   - Ensure all required Qt modules are installed

3. **Runtime crashes**:
   - Check that all Qt6 DLLs are accessible
   - Verify video file integrity

### Getting Help

If you encounter issues:
1. Check the Qt documentation for multimedia requirements
2. Verify your Qt installation includes multimedia components
3. Test with different video files to isolate format-specific issues

## License

This project is provided as-is for educational purposes. Feel free to modify and extend it according to your needs.

## Contributing

This is a demonstration project, but improvements and bug fixes are welcome. Consider areas like:
- Better error handling
- Additional video format support
- UI/UX improvements
- Performance optimizations
