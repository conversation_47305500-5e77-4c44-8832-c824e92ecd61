#ifndef TIMELINE_H
#define TIMELINE_H

#include <QWidget>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QPainter>
#include <QScrollBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QSpinBox>
#include <QTimer>
#include <QRubberBand>

struct VideoClip {
    qint64 startTime;
    qint64 endTime;
    qint64 duration;
    QString name;
    QColor color;
    bool selected;

    VideoClip() : startTime(0), endTime(0), duration(0), color(Qt::blue), selected(false) {}
    VideoClip(qint64 start, qint64 end, const QString &clipName = "Clip")
        : startTime(start), endTime(end), duration(end - start), name(clipName), color(Qt::blue), selected(false) {}
};

class TimelineWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TimelineWidget(QWidget *parent = nullptr);
    
    void setDuration(qint64 duration);
    void setPosition(qint64 position);
    void addClip(const VideoClip &clip);
    void removeSelectedClips();
    void clearClips();
    
    // Zoom and scroll
    void setZoomLevel(double zoom);
    double zoomLevel() const { return m_zoomLevel; }
    
    // Selection
    QList<VideoClip> selectedClips() const;
    void selectClip(int index);
    void clearSelection();

signals:
    void positionChanged(qint64 position);
    void clipSelected(int index);
    void clipDoubleClicked(int index);
    void selectionChanged();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    void drawTimeRuler(QPainter &painter);
    void drawPlayhead(QPainter &painter);
    void drawClips(QPainter &painter);
    void drawSelection(QPainter &painter);
    
    qint64 pixelToTime(int pixel) const;
    int timeToPixel(qint64 time) const;
    QString formatTime(qint64 milliseconds) const;
    
    int findClipAt(const QPoint &pos) const;
    void updateScrollBars();

private:
    // Timeline data
    qint64 m_duration;
    qint64 m_position;
    QList<VideoClip> m_clips;
    
    // Display properties
    double m_zoomLevel;
    int m_scrollOffset;
    int m_rulerHeight;
    int m_trackHeight;
    int m_playheadWidth;
    
    // Mouse interaction
    bool m_dragging;
    bool m_selecting;
    QPoint m_dragStart;
    QPoint m_lastMousePos;
    QRubberBand *m_rubberBand;
    
    // Colors
    QColor m_backgroundColor;
    QColor m_rulerColor;
    QColor m_playheadColor;
    QColor m_selectionColor;
    QColor m_gridColor;
};

class Timeline : public QWidget
{
    Q_OBJECT

public:
    explicit Timeline(QWidget *parent = nullptr);
    
    void setDuration(qint64 duration);
    void setPosition(qint64 position);
    
    // Clip operations
    void addClip(qint64 startTime, qint64 endTime, const QString &name = "Clip");
    void splitClipAt(qint64 position);
    void removeSelectedClips();
    void clearTimeline();

signals:
    void positionChanged(qint64 position);
    void clipModified();

private slots:
    void onZoomChanged(int value);
    void onTimelinePositionChanged(qint64 position);
    void onClipSelected(int index);

private:
    void setupUI();
    void setupControls();

    // UI components
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_controlsLayout;
    TimelineWidget *m_timelineWidget;
    QScrollBar *m_horizontalScrollBar;
    
    // Controls
    QLabel *m_zoomLabel;
    QSpinBox *m_zoomSpinBox;
    QPushButton *m_splitButton;
    QPushButton *m_deleteButton;
    QPushButton *m_clearButton;
    
    // Current state
    qint64 m_currentDuration;
    qint64 m_currentPosition;
};

#endif // TIMELINE_H
