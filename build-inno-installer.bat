@echo off
REM Build installer using Inno Setup for Puttis VideoEditor
REM Inno Setup creates more modern installers than NSIS

echo ========================================
echo Puttis VideoEditor Installer Builder (Inno Setup)
echo ========================================
echo.

REM Check if Inno Setup is installed
set INNO_PATH="C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist %INNO_PATH% (
    echo ERROR: Inno Setup not found at %INNO_PATH%
    echo.
    echo Please install Inno Setup from: https://jrsoftware.org/isinfo.php
    echo After installation, run this script again.
    echo.
    pause
    exit /b 1
)

REM Check if executable exists
if not exist "build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\VideoEditor.exe" (
    echo ERROR: VideoEditor.exe not found!
    echo Please build the project first in Qt Creator.
    echo.
    pause
    exit /b 1
)

echo Building installer with Inno Setup...
echo.

REM Build the installer
%INNO_PATH% installer.iss

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS! Modern installer created!
    echo ========================================
    echo.
    echo Installer file: PuttisVideoEditor_Setup.exe
    echo.
    echo Features of this installer:
    echo - Modern Windows 10/11 style interface
    echo - Automatic Qt DLL deployment
    echo - Start Menu and Desktop shortcuts
    echo - File association for .vep files
    echo - Add/Remove Programs integration
    echo - Clean uninstaller
    echo - User-level installation (no admin required)
    echo.
    echo The installer is ready for distribution!
    echo.
) else (
    echo.
    echo ERROR: Failed to build installer!
    echo Check the error messages above.
    echo.
)

pause
