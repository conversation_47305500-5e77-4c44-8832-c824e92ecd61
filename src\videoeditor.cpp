#include "videoeditor.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QMimeData>
#include <QUrl>
#include <QDebug>

VideoEditor::VideoEditor(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_splitter(nullptr)
    , m_projectModified(false)
{
    setupUI();
    setAcceptDrops(true);
}

void VideoEditor::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // Create splitter for panels
    m_splitter = new QSplitter(Qt::Vertical, this);
    m_mainLayout->addWidget(m_splitter);
    
    setupFilePanel();
    setupEditingPanel();
    setupExportPanel();
    setupPropertiesPanel();
    
    // Set initial splitter sizes
    m_splitter->setSizes({150, 100, 120, 100});
}

void VideoEditor::setupFilePanel()
{
    m_fileGroup = new QGroupBox("Project Files", this);
    m_fileLayout = new QVBoxLayout(m_fileGroup);
    
    // File list
    m_fileList = new QListWidget(this);
    m_fileList->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_fileList->setDragDropMode(QAbstractItemView::DropOnly);
    m_fileLayout->addWidget(m_fileList);
    
    // File buttons
    m_fileButtonsLayout = new QHBoxLayout();
    
    m_addFileButton = new QPushButton("Add Files...", this);
    m_addFileButton->setToolTip("Add video files to the project");
    
    m_removeFileButton = new QPushButton("Remove", this);
    m_removeFileButton->setEnabled(false);
    m_removeFileButton->setToolTip("Remove selected files from project");
    
    m_clearFilesButton = new QPushButton("Clear All", this);
    m_clearFilesButton->setToolTip("Remove all files from project");
    
    m_fileButtonsLayout->addWidget(m_addFileButton);
    m_fileButtonsLayout->addWidget(m_removeFileButton);
    m_fileButtonsLayout->addWidget(m_clearFilesButton);
    m_fileButtonsLayout->addStretch();
    
    m_fileLayout->addLayout(m_fileButtonsLayout);
    m_splitter->addWidget(m_fileGroup);
    
    // Connect signals
    connect(m_addFileButton, &QPushButton::clicked, this, &VideoEditor::onAddFileClicked);
    connect(m_removeFileButton, &QPushButton::clicked, this, &VideoEditor::onRemoveFileClicked);
    connect(m_clearFilesButton, &QPushButton::clicked, this, &VideoEditor::onClearFilesClicked);
    connect(m_fileList, &QListWidget::itemSelectionChanged, this, &VideoEditor::onFileSelectionChanged);
}

void VideoEditor::setupEditingPanel()
{
    m_editingGroup = new QGroupBox("Editing Tools", this);
    m_editingLayout = new QVBoxLayout(m_editingGroup);
    
    // Create editing buttons in rows
    QHBoxLayout *row1 = new QHBoxLayout();
    m_cutButton = new QPushButton("Cut", this);
    m_cutButton->setEnabled(false);
    m_cutButton->setToolTip("Cut selected clip");
    
    m_copyButton = new QPushButton("Copy", this);
    m_copyButton->setEnabled(false);
    m_copyButton->setToolTip("Copy selected clip");
    
    m_pasteButton = new QPushButton("Paste", this);
    m_pasteButton->setEnabled(false);
    m_pasteButton->setToolTip("Paste clip at current position");
    
    row1->addWidget(m_cutButton);
    row1->addWidget(m_copyButton);
    row1->addWidget(m_pasteButton);
    
    QHBoxLayout *row2 = new QHBoxLayout();
    m_trimButton = new QPushButton("Trim", this);
    m_trimButton->setEnabled(false);
    m_trimButton->setToolTip("Trim selected clip");
    
    m_splitButton = new QPushButton("Split", this);
    m_splitButton->setEnabled(false);
    m_splitButton->setToolTip("Split clip at current position");
    
    m_mergeButton = new QPushButton("Merge", this);
    m_mergeButton->setEnabled(false);
    m_mergeButton->setToolTip("Merge selected clips");
    
    row2->addWidget(m_trimButton);
    row2->addWidget(m_splitButton);
    row2->addWidget(m_mergeButton);
    
    m_editingLayout->addLayout(row1);
    m_editingLayout->addLayout(row2);
    m_splitter->addWidget(m_editingGroup);
}

void VideoEditor::setupExportPanel()
{
    m_exportGroup = new QGroupBox("Export Settings", this);
    m_exportLayout = new QVBoxLayout(m_exportGroup);
    
    // Quality settings
    m_qualityLayout = new QHBoxLayout();
    m_qualityLabel = new QLabel("Quality:", this);
    m_qualityCombo = new QComboBox(this);
    m_qualityCombo->addItems({"Low", "Medium", "High", "Ultra"});
    m_qualityCombo->setCurrentText("High");
    
    m_qualityLayout->addWidget(m_qualityLabel);
    m_qualityLayout->addWidget(m_qualityCombo);
    m_qualityLayout->addStretch();
    
    // Resolution settings
    m_resolutionLayout = new QHBoxLayout();
    m_resolutionLabel = new QLabel("Resolution:", this);
    m_resolutionCombo = new QComboBox(this);
    m_resolutionCombo->addItems({"720x480", "1280x720", "1920x1080", "2560x1440", "3840x2160"});
    m_resolutionCombo->setCurrentText("1920x1080");
    
    m_resolutionLayout->addWidget(m_resolutionLabel);
    m_resolutionLayout->addWidget(m_resolutionCombo);
    m_resolutionLayout->addStretch();
    
    // Bitrate settings
    m_bitrateLayout = new QHBoxLayout();
    m_bitrateLabel = new QLabel("Bitrate (kbps):", this);
    m_bitrateSpinBox = new QSpinBox(this);
    m_bitrateSpinBox->setRange(500, 50000);
    m_bitrateSpinBox->setValue(5000);
    m_bitrateSpinBox->setSingleStep(500);
    
    m_bitrateLayout->addWidget(m_bitrateLabel);
    m_bitrateLayout->addWidget(m_bitrateSpinBox);
    m_bitrateLayout->addStretch();
    
    // Export button and progress
    m_exportButton = new QPushButton("Export Video...", this);
    m_exportButton->setEnabled(false);
    
    m_exportProgress = new QProgressBar(this);
    m_exportProgress->setVisible(false);
    
    m_exportLayout->addLayout(m_qualityLayout);
    m_exportLayout->addLayout(m_resolutionLayout);
    m_exportLayout->addLayout(m_bitrateLayout);
    m_exportLayout->addWidget(m_exportButton);
    m_exportLayout->addWidget(m_exportProgress);
    
    m_splitter->addWidget(m_exportGroup);
    
    // Connect signals
    connect(m_qualityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &VideoEditor::onQualityChanged);
    connect(m_resolutionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &VideoEditor::onResolutionChanged);
    connect(m_exportButton, &QPushButton::clicked, this, &VideoEditor::onExportClicked);
}

void VideoEditor::setupPropertiesPanel()
{
    m_propertiesGroup = new QGroupBox("File Properties", this);
    m_propertiesLayout = new QVBoxLayout(m_propertiesGroup);
    
    m_propertiesText = new QTextEdit(this);
    m_propertiesText->setReadOnly(true);
    m_propertiesText->setMaximumHeight(100);
    m_propertiesText->setPlainText("Select a file to view its properties...");
    
    m_propertiesLayout->addWidget(m_propertiesText);
    m_splitter->addWidget(m_propertiesGroup);
}

void VideoEditor::addVideoFile(const QString &filePath)
{
    if (!m_videoFiles.contains(filePath)) {
        m_videoFiles.append(filePath);
        
        QFileInfo fileInfo(filePath);
        QListWidgetItem *item = new QListWidgetItem(fileInfo.fileName());
        item->setData(Qt::UserRole, filePath);
        item->setToolTip(filePath);
        m_fileList->addItem(item);
        
        m_projectModified = true;
        m_exportButton->setEnabled(true);
        
        emit fileAdded(filePath);
    }
}

void VideoEditor::removeSelectedFiles()
{
    QList<QListWidgetItem*> selectedItems = m_fileList->selectedItems();
    for (QListWidgetItem *item : selectedItems) {
        QString filePath = item->data(Qt::UserRole).toString();
        m_videoFiles.removeAll(filePath);
        delete item;
        emit fileRemoved(filePath);
    }
    
    m_projectModified = true;
    m_exportButton->setEnabled(!m_videoFiles.isEmpty());
    updateFileInfo();
}

void VideoEditor::clearFileList()
{
    m_videoFiles.clear();
    m_fileList->clear();
    m_projectModified = true;
    m_exportButton->setEnabled(false);
    m_propertiesText->setPlainText("Select a file to view its properties...");
}

void VideoEditor::newProject()
{
    if (m_projectModified) {
        int ret = QMessageBox::question(this, "New Project",
            "Current project has unsaved changes. Continue?",
            QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::No) {
            return;
        }
    }
    
    clearFileList();
    m_currentProject.clear();
    m_projectModified = false;
}

void VideoEditor::loadProject(const QString &projectPath)
{
    // TODO: Implement project loading
    QMessageBox::information(this, "Load Project", 
        "Project loading functionality will be implemented in a future version.");
}

void VideoEditor::saveProject(const QString &projectPath)
{
    // TODO: Implement project saving
    QMessageBox::information(this, "Save Project", 
        "Project saving functionality will be implemented in a future version.");
}

void VideoEditor::exportVideo(const QString &outputPath)
{
    // TODO: Implement video export
    QMessageBox::information(this, "Export Video", 
        "Video export functionality will be implemented in a future version.");
}

void VideoEditor::onAddFileClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
        "Add Video Files",
        "",
        "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;All Files (*)");
    
    for (const QString &fileName : fileNames) {
        addVideoFile(fileName);
    }
}

void VideoEditor::onRemoveFileClicked()
{
    removeSelectedFiles();
}

void VideoEditor::onClearFilesClicked()
{
    int ret = QMessageBox::question(this, "Clear All Files",
        "Remove all files from the project?",
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        clearFileList();
    }
}

void VideoEditor::onFileSelectionChanged()
{
    bool hasSelection = !m_fileList->selectedItems().isEmpty();
    m_removeFileButton->setEnabled(hasSelection);
    
    if (hasSelection) {
        updateFileInfo();
    } else {
        m_propertiesText->setPlainText("Select a file to view its properties...");
    }
}

void VideoEditor::onExportClicked()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "Export Video",
        "",
        "MP4 Video (*.mp4);;AVI Video (*.avi);;MOV Video (*.mov);;All Files (*)");
    
    if (!fileName.isEmpty()) {
        exportVideo(fileName);
    }
}

void VideoEditor::onQualityChanged(int index)
{
    m_exportSettings.quality = m_qualityCombo->currentText();
    updateExportSettings();
}

void VideoEditor::onResolutionChanged(int index)
{
    m_exportSettings.resolution = m_resolutionCombo->currentText();
    updateExportSettings();
}

void VideoEditor::updateFileInfo()
{
    QList<QListWidgetItem*> selectedItems = m_fileList->selectedItems();
    if (selectedItems.isEmpty()) {
        return;
    }
    
    QListWidgetItem *item = selectedItems.first();
    QString filePath = item->data(Qt::UserRole).toString();
    QFileInfo fileInfo(filePath);
    
    QString info = QString(
        "File: %1\n"
        "Path: %2\n"
        "Size: %3 MB\n"
        "Modified: %4\n"
        "Format: %5"
    ).arg(fileInfo.fileName())
     .arg(fileInfo.absolutePath())
     .arg(fileInfo.size() / (1024 * 1024))
     .arg(fileInfo.lastModified().toString())
     .arg(fileInfo.suffix().toUpper());
    
    m_propertiesText->setPlainText(info);
}

void VideoEditor::updateExportSettings()
{
    // Update bitrate based on quality and resolution
    QString quality = m_exportSettings.quality;
    QString resolution = m_exportSettings.resolution;
    
    int bitrate = 2000; // Default
    
    if (resolution.contains("720")) {
        bitrate = quality == "Ultra" ? 8000 : quality == "High" ? 5000 : quality == "Medium" ? 3000 : 1500;
    } else if (resolution.contains("1080")) {
        bitrate = quality == "Ultra" ? 12000 : quality == "High" ? 8000 : quality == "Medium" ? 5000 : 2500;
    } else if (resolution.contains("1440")) {
        bitrate = quality == "Ultra" ? 20000 : quality == "High" ? 15000 : quality == "Medium" ? 10000 : 5000;
    } else if (resolution.contains("2160")) {
        bitrate = quality == "Ultra" ? 40000 : quality == "High" ? 25000 : quality == "Medium" ? 15000 : 8000;
    }
    
    m_bitrateSpinBox->setValue(bitrate);
    m_exportSettings.bitrate = bitrate;
}
