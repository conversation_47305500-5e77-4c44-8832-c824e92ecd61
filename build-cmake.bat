@echo off
REM CMake build script for VideoEditor
REM Adjust paths according to your Qt installation

REM Set Qt path (adjust this to your Qt installation)
set QT_DIR=C:\Qt\6.9.1\mingw_64
set PATH=%QT_DIR%\bin;%PATH%

REM Set MinGW path (adjust this to your MinGW installation)
set MINGW_DIR=C:\Qt\Tools\mingw1310_64
set PATH=%MINGW_DIR%\bin;%PATH%

REM Create and enter build directory
if exist build-cmake rmdir /s /q build-cmake
mkdir build-cmake
cd build-cmake

REM Configure with CMake
echo Configuring with CMake...
cmake .. -G "MinGW Makefiles" -DCMAKE_PREFIX_PATH="%QT_DIR%"

REM Build
echo Building...
cmake --build .

echo.
echo Build complete! Executable should be in build-cmake directory.
pause
