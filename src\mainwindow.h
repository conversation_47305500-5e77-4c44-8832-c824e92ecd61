#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QFileDialog>
#include <QMessageBox>
#include <QProgressBar>

class VideoPlayer;
class Timeline;
class VideoEditor;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void openFile();
    void saveProject();
    void exportVideo();
    void about();
    void onVideoLoaded(const QString &fileName);
    void onVideoError(const QString &error);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void createCentralWidget();
    void createControlsWidget();
    void connectSignals();
    
    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QSplitter *m_videoSplitter;
    
    // Video components
    VideoPlayer *m_videoPlayer;
    Timeline *m_timeline;
    VideoEditor *m_videoEditor;
    
    // Controls
    QWidget *m_controlsWidget;
    QPushButton *m_playButton;
    QPushButton *m_pauseButton;
    QPushButton *m_stopButton;
    QSlider *m_positionSlider;
    QLabel *m_timeLabel;
    QLabel *m_durationLabel;
    
    // Menu and toolbar
    QMenuBar *m_menuBar;
    QToolBar *m_toolBar;
    QStatusBar *m_statusBar;
    QProgressBar *m_progressBar;
    
    // Actions
    QAction *m_openAction;
    QAction *m_saveAction;
    QAction *m_exportAction;
    QAction *m_exitAction;
    QAction *m_aboutAction;
    
    // Current file
    QString m_currentFile;
    bool m_isVideoLoaded;
};

#endif // MAINWINDOW_H
