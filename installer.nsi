;NSIS Installer Script for Puttis VideoEditor
;This creates a professional installer that handles Qt DLL deployment

!define APP_NAME "Puttis VideoEditor"
!define APP_VERSION "1.2.1"
!define PUBLISHER "PuttisStudios"
!define WEB_SITE "https://github.com/CyberzIsOn/"
!define APP_EXE "VideoEditor.exe"
!define INSTALL_TYPE "SetShellVarContext current"
!define REG_ROOT "HKCU"
!define REG_APP_PATH "Software\Microsoft\Windows\CurrentVersion\App Paths\${APP_EXE}"
!define UNINSTALL_PATH "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"

;--------------------------------
;General

;Name and file
Name "${APP_NAME}"
OutFile "PuttisVideoEditor_Setup.exe"
Unicode True

;Default installation folder
InstallDir "$LOCALAPPDATA\${APP_NAME}"

;Get installation folder from registry if available
InstallDirRegKey ${REG_ROOT} "${REG_APP_PATH}" ""

;Request application privileges for Windows Vista
RequestExecutionLevel user

;--------------------------------
;Variables

Var StartMenuFolder

;--------------------------------
;Interface Settings

!include "MUI2.nsh"

!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

;--------------------------------
;Pages

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY

;Start Menu Folder Page Configuration
!define MUI_STARTMENUPAGE_REGISTRY_ROOT ${REG_ROOT}
!define MUI_STARTMENUPAGE_REGISTRY_KEY "${UNINSTALL_PATH}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "StartMenuFolder"

!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
;Languages

!insertmacro MUI_LANGUAGE "English"

;--------------------------------
;Installer Sections

Section "Puttis VideoEditor" SecMain

  SetOutPath "$INSTDIR"
  
  ;Copy main executable
  File "build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\VideoEditor.exe"
  
  ;Copy Qt DLLs (adjust paths as needed)
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Core.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Gui.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Widgets.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Multimedia.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6MultimediaWidgets.dll"
  File "C:\Qt\6.9.1\mingw_64\bin\Qt6Network.dll"
  
  ;Copy MinGW runtime DLLs
  File "C:\Qt\Tools\mingw1310_64\bin\libgcc_s_seh-1.dll"
  File "C:\Qt\Tools\mingw1310_64\bin\libstdc++-6.dll"
  File "C:\Qt\Tools\mingw1310_64\bin\libwinpthread-1.dll"
  
  ;Create platforms directory and copy platform plugin
  CreateDirectory "$INSTDIR\platforms"
  SetOutPath "$INSTDIR\platforms"
  File "C:\Qt\6.9.1\mingw_64\plugins\platforms\qwindows.dll"
  
  ;Back to main directory
  SetOutPath "$INSTDIR"
  
  ;Copy documentation
  File "README.md"
  
  ;Store installation folder
  WriteRegStr ${REG_ROOT} "${REG_APP_PATH}" "" $INSTDIR
  WriteRegStr ${REG_ROOT} "${REG_APP_PATH}" "Path" "$INSTDIR\${APP_EXE}"
  
  ;Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ;Create registry entries for Add/Remove Programs
  WriteRegStr ${REG_ROOT} "${UNINSTALL_PATH}" "DisplayName" "Puttis VideoEditor"
  WriteRegStr ${REG_ROOT} "${UNINSTALL_PATH}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr ${REG_ROOT} "${UNINSTALL_PATH}" "DisplayIcon" "$INSTDIR\${APP_EXE}"
  WriteRegStr ${REG_ROOT} "${UNINSTALL_PATH}" "Publisher" "PuttisStudios"
  WriteRegStr ${REG_ROOT} "${UNINSTALL_PATH}" "DisplayVersion" "1.2.1"
  WriteRegDWORD ${REG_ROOT} "${UNINSTALL_PATH}" "NoModify" 1
  WriteRegDWORD ${REG_ROOT} "${UNINSTALL_PATH}" "NoRepair" 1
  
  ;Create Start Menu shortcuts
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
  !insertmacro MUI_STARTMENU_WRITE_END
  
  ;Create Desktop shortcut
  CreateShortcut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}"

SectionEnd

;--------------------------------
;Uninstaller Section

Section "Uninstall"

  ;Remove files and uninstaller
  Delete "$INSTDIR\${APP_EXE}"
  Delete "$INSTDIR\*.dll"
  Delete "$INSTDIR\platforms\*.dll"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\Uninstall.exe"
  
  ;Remove directories
  RMDir "$INSTDIR\platforms"
  RMDir "$INSTDIR"
  
  ;Remove Start Menu shortcuts
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"
  
  ;Remove Desktop shortcut
  Delete "$DESKTOP\${APP_NAME}.lnk"
  
  ;Remove registry entries
  DeleteRegKey ${REG_ROOT} "${REG_APP_PATH}"
  DeleteRegKey ${REG_ROOT} "${UNINSTALL_PATH}"

SectionEnd
