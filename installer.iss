; Inno Setup Script for VideoEditor
; This creates a modern, professional installer

#define MyAppName "VideoEditor"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Qt Video Editor Team"
#define MyAppURL "https://github.com/yourusername/videoeditor"
#define MyAppExeName "VideoEditor.exe"
#define MyAppAssocName MyAppName + " Video Project"
#define MyAppAssocExt ".vep"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
ChangesAssociations=yes
DisableProgramGroupPage=yes
LicenseFile=LICENSE.txt
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
OutputDir=.
OutputBaseFilename=VideoEditor_Setup
SetupIconFile=C:\Qt\6.9.1\mingw_64\bin\Qt6Core.dll,0
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; Main executable
Source: "build\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion

; Qt DLLs - Core libraries
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6Core.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6Gui.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6Widgets.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6Multimedia.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6MultimediaWidgets.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\6.9.1\mingw_64\bin\Qt6Network.dll"; DestDir: "{app}"; Flags: ignoreversion

; MinGW runtime DLLs
Source: "C:\Qt\Tools\mingw1310_64\bin\libgcc_s_seh-1.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\Tools\mingw1310_64\bin\libstdc++-6.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Qt\Tools\mingw1310_64\bin\libwinpthread-1.dll"; DestDir: "{app}"; Flags: ignoreversion

; Platform plugins
Source: "C:\Qt\6.9.1\mingw_64\plugins\platforms\qwindows.dll"; DestDir: "{app}\platforms"; Flags: ignoreversion

; Documentation
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion

[Registry]
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".vep"; ValueData: ""

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent
