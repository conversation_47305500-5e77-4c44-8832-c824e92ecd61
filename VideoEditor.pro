QT += core widgets multimedia multimediawidgets

CONFIG += c++17

TARGET = VideoEditor
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp \
    src/videoplayer.cpp \
    src/timeline.cpp \
    src/videoeditor.cpp

HEADERS += \
    src/mainwindow.h \
    src/videoplayer.h \
    src/timeline.h \
    src/videoeditor.h

FORMS += \
    ui/mainwindow.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# Include paths
INCLUDEPATH += src

# Resources (if any)
# RESOURCES += resources.qrc

# Platform-specific configurations
win32 {
    # Windows-specific settings
    RC_ICONS = icon.ico
    VERSION = *******
    QMAKE_TARGET_COMPANY = "Qt Video Editor"
    QMAKE_TARGET_PRODUCT = "Video Editor"
    QMAKE_TARGET_DESCRIPTION = "Basic Video Editing Application"
    QMAKE_TARGET_COPYRIGHT = "Copyright 2025"
}

macx {
    # macOS-specific settings
    ICON = icon.icns
    QMAKE_INFO_PLIST = Info.plist
}

unix:!macx {
    # Linux-specific settings
    target.path = /usr/local/bin
    INSTALLS += target
}
